import logging
import sys
from datetime import datetime
from typing import Any, Dict, Optional
from enum import Enum

class LogLevel(Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class CustomLogFormatter(logging.Formatter):
    def __init__(self, format_string: str = None):
        if format_string is None:
            format_string = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        super().__init__(format_string)
        
    def format(self, record):
        return super().format(record)

class LoggingManager:
    def __init__(self, logger_name: str = "AppLogger"):
        self.logger = logging.getLogger(logger_name)
        self.handlers = []
        self.log_history = []
        
    def setup_console_logging(self, level: LogLevel = LogLevel.INFO):
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, level.value))
        formatter = CustomLogFormatter()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        self.handlers.append(console_handler)
        
    def setup_file_logging(self, filename: str, level: LogLevel = LogLevel.INFO):
        file_handler = logging.FileHandler(filename)
        file_handler.setLevel(getattr(logging, level.value))
        formatter = CustomLogFormatter()
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        self.handlers.append(file_handler)
        
    def log_message(self, level: LogLevel, message: str, extra_data: Dict[str, Any] = None):
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "level": level.value,
            "message": message,
            "extra_data": extra_data or {}
        }
        self.log_history.append(log_entry)
        
        log_method = getattr(self.logger, level.value.lower())
        if extra_data:
            log_method(f"{message} | Extra: {extra_data}")
        else:
            log_method(message)
            
    def debug(self, message: str, **kwargs):
        self.log_message(LogLevel.DEBUG, message, kwargs)
        
    def info(self, message: str, **kwargs):
        self.log_message(LogLevel.INFO, message, kwargs)
        
    def warning(self, message: str, **kwargs):
        self.log_message(LogLevel.WARNING, message, kwargs)
        
    def error(self, message: str, **kwargs):
        self.log_message(LogLevel.ERROR, message, kwargs)
        
    def critical(self, message: str, **kwargs):
        self.log_message(LogLevel.CRITICAL, message, kwargs)
        
    def get_log_history(self):
        return self.log_history
        
    def clear_history(self):
        self.log_history = []
        
    def set_level(self, level: LogLevel):
        self.logger.setLevel(getattr(logging, level.value))
        for handler in self.handlers:
            handler.setLevel(getattr(logging, level.value))

class StructuredLogger:
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.session_id = None
        
    def set_session(self, session_id: str):
        self.session_id = session_id
        
    def create_log_entry(self, level: str, event: str, **kwargs) -> Dict[str, Any]:
        entry = {
            "timestamp": datetime.now().isoformat(),
            "service": self.service_name,
            "level": level,
            "event": event,
            "session_id": self.session_id,
            "data": kwargs
        }
        return entry
        
    def log_request(self, method: str, endpoint: str, status_code: int, response_time: float = None):
        entry = self.create_log_entry(
            "INFO",
            "http_request",
            method=method,
            endpoint=endpoint,
            status_code=status_code,
            response_time=response_time
        )
        print(f"REQUEST LOG: {entry}")
        
    def log_database_query(self, query_type: str, table: str, execution_time: float = None):
        entry = self.create_log_entry(
            "DEBUG",
            "database_query",
            query_type=query_type,
            table=table,
            execution_time=execution_time
        )
        print(f"DB LOG: {entry}")
        
    def log_error(self, error_type: str, error_message: str, stack_trace: str = None):
        entry = self.create_log_entry(
            "ERROR",
            "application_error",
            error_type=error_type,
            error_message=error_message,
            stack_trace=stack_trace
        )
        print(f"ERROR LOG: {entry}")
        
    def log_user_action(self, user_id: str, action: str, **details):
        entry = self.create_log_entry(
            "INFO",
            "user_action",
            user_id=user_id,
            action=action,
            **details
        )
        print(f"USER LOG: {entry}")

def create_logger(name: str, level: LogLevel = LogLevel.INFO) -> LoggingManager:
    logger_manager = LoggingManager(name)
    logger_manager.setup_console_logging(level)
    return logger_manager

def log_function_call(func_name: str, args: tuple = None, kwargs: dict = None):
    print(f"FUNCTION CALL: {func_name} | Args: {args} | Kwargs: {kwargs}")

def log_performance_metric(metric_name: str, value: float, unit: str = "ms"):
    entry = {
        "timestamp": datetime.now().isoformat(),
        "metric": metric_name,
        "value": value,
        "unit": unit
    }
    print(f"PERFORMANCE: {entry}")

app_logger = create_logger("MainApp")
structured_logger = StructuredLogger("inspection-service") 