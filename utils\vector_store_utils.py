from langchain_community.vectorstores import Chroma
from langchain.schema import Document
import os
import uuid
from langchain_huggingface import HuggingFaceEmbeddings
import torch
from config import Config

class VectorStoreUtils:
    def __init__(self, persist_directory: str, collection_name: str = "default", embedding=None):
        self.persist_directory = persist_directory
        self.collection_name = collection_name
        print("cuda" if torch.cuda.is_available() else "cpu")
        self.embedding = embedding or  HuggingFaceEmbeddings(
            model_name= Config.get("EMBEDDING_MODEL_PATH") or  "BAAI/bge-base-zh",
            model_kwargs={"device": "cuda" if torch.cuda.is_available() else "cpu"},
            encode_kwargs={"normalize_embeddings": True}
        )
        os.makedirs(persist_directory, exist_ok=True)

        self.vectorstore = Chroma(
            collection_name=self.collection_name,
            persist_directory=self.persist_directory,
            embedding_function=self.embedding
        )

    def add_documents(self, texts: list[str], metadatas: list[dict] = None):
        metadatas = metadatas or [{}] * len(texts)
        ids = [str(uuid.uuid4()) for _ in texts]
        self.vectorstore.add_texts(texts=texts, metadatas=metadatas, ids=ids)
        self.vectorstore.persist()
        return ids

    def delete_document(self, doc_id: str):
        self.vectorstore.delete([doc_id])
        self.vectorstore.persist()

    def update_document(self, doc_id: str, new_text: str, new_metadata: dict = None):
        self.delete_document(doc_id)
        self.vectorstore.add_texts(
            texts=[new_text],
            metadatas=[new_metadata or {}],
            ids=[doc_id]
        )
        self.vectorstore.persist()

    def search(self, query: str, k: int = 4, with_score: bool = False):
        if with_score:
            return self.vectorstore.similarity_search_with_score(query, k=k)
        else:
            return self.vectorstore.similarity_search(query, k=k)

    def list_all_documents(self):
        raise NotImplementedError("Chroma doesn't natively support full document listing.")

