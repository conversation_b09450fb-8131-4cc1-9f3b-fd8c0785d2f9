from flask import Flask, render_template, request, Response, jsonify
import json
import time
from service.RAG_agent import RAGAgent
from service.vector_store_manager import VectorStoreManager
from typing import List, Tuple, Generator

app = Flask(__name__)

vector_manager = VectorStoreManager()

chat_sessions = {}

class StreamingRAGAgent(RAGAgent):
    
    def stream_run(self, query: str, history: List[Tuple[str, str]] = None) -> Generator[str, None, None]:
        history = history or []
        
        summary_query = self.condense_query(query, history)
        print(f"Condensed query: {summary_query}")
        
        from concurrent.futures import ThreadPoolExecutor
        with ThreadPoolExecutor() as executor:
            future_qa = executor.submit(self.search_with_threshold, self.vector_manager.search_qa, summary_query)
            # future_chat = executor.submit(self.search_with_threshold, self.vector_manager.search_chat, summary_query)
            
            qa_docs = future_qa.result()
            # chat_docs = future_chat.result()
            chat_docs = []
        
        messages = self.build_messages(query, history, qa_docs, chat_docs)
        
        for chunk in self.llm.stream(messages):
            if chunk.content:
                yield chunk.content
streaming_agent = StreamingRAGAgent(vector_manager=vector_manager)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/chat', methods=['POST'])
def chat():
    data = request.get_json()
    user_message = data.get('message', '').strip()
    session_id = data.get('session_id', 'default')
    
    if not user_message:
        return jsonify({'error': '消息不能为空'}), 400
    
    if session_id not in chat_sessions:
        chat_sessions[session_id] = []
    
    history = chat_sessions[session_id]
    
    def generate():
        try:
            yield f"data: {json.dumps({'type': 'start', 'message': ''})}\n\n"
            
            full_response = ""
            for chunk in streaming_agent.stream_run(user_message, history):
                full_response += chunk
                yield f"data: {json.dumps({'type': 'chunk', 'message': chunk})}\n\n"
                time.sleep(0.01)
            
            history.append(("user", user_message))
            history.append(("ai", full_response))
            print(f"user===={user_message}")
            print(f"ai===={full_response}")
            
            if len(history) > 40:
                history[:] = history[-40:]
            
            yield f"data: {json.dumps({'type': 'end', 'message': full_response})}\n\n"
            
        except Exception as e:
            print(f"Error in chat: {e}")
            yield f"data: {json.dumps({'type': 'error', 'message': '抱歉，服务出现问题，请稍后重试'})}\n\n"
    
    return Response(
        generate(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*'
        }
    )

@app.route('/history/<session_id>')
def get_history(session_id):
    history = chat_sessions.get(session_id, [])
    return jsonify({'history': history})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000) 