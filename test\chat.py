from service.RAG_agent import RAGAgent
from service.vector_store_manager import VectorStoreManager
vector_manager = VectorStoreManager()
agent = RAGAgent(vector_manager=vector_manager)

history=[]

print("请输入问题，输入 exit 退出")
while True:
    query = input("用户：")
    if query.strip().lower() == "exit":
        break
    answer = agent.run(query, history)
    print(f"AI：{answer}")
    history.append(("user", query))
    history.append(("ai", answer))