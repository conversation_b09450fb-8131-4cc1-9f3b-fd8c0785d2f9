from flask import Flask, request, Response, jsonify
from flask_cors import CORS
from flask_socketio import Socket<PERSON>, emit, disconnect
import json
import time
import uuid
from typing import List, Tuple, Generator, Dict, Any
from service.RAG_agent import RAGAgent
from service.vector_store_manager import VectorStoreManager
from service.DialogueSummarizer import DialogueSummarizer

app = Flask(__name__)
CORS(app)

socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

vector_manager = VectorStoreManager()
dialogue_summarizer = DialogueSummarizer()

class StreamingRAGAgent(RAGAgent):
    
    def stream_run(self, query: str, history: List[Tuple[str, str]] = None) -> Generator[str, None, None]:
        history = history or []
        
        summary_query = self.condense_query(query, history)
        
        from concurrent.futures import ThreadPoolExecutor
        with ThreadPoolExecutor() as executor:
            future_qa = executor.submit(self.search_with_threshold, self.vector_manager.search_qa, summary_query)
            
            qa_docs = future_qa.result()
            chat_docs = []
        
        messages = self.build_messages(query, history, qa_docs, chat_docs)
        
        for chunk in self.llm.stream(messages):
            if chunk.content:
                yield chunk.content

streaming_agent = StreamingRAGAgent(vector_manager=vector_manager)

def validate_request_data(data: Dict[str, Any], required_fields: List[str]) -> tuple[bool, str]:
    if not data:
        return False, "请求数据不能为空"
    
    for field in required_fields:
        if field not in data:
            return False, f"缺少必需字段: {field}"
        if not data[field]:
            return False, f"字段 {field} 不能为空"
    
    return True, ""

def error_response(message: str, status_code: int = 400) -> tuple:
    return jsonify({
        "success": False,
        "error": message,
        "data": None
    }), status_code

def success_response(data: Any = None, message: str = "操作成功") -> Dict[str, Any]:
    return jsonify({
        "success": True,
        "message": message,
        "data": data
    })

@app.route('/api/documents', methods=['POST'])
def add_documents():
    try:
        data = request.get_json()
        
        is_valid, error_msg = validate_request_data(data, ['texts'])
        if not is_valid:
            return error_response(error_msg)
        
        texts = data['texts']
        metadatas = data.get('metadatas', [])
        
        if not isinstance(texts, list) or len(texts) == 0:
            return error_response("texts必须是非空列表")
        
        if metadatas and len(metadatas) != len(texts):
            return error_response("metadatas长度必须与texts长度一致")
        
        doc_ids = vector_manager.add_qa(texts, metadatas if metadatas else None)
        
        return success_response({
            "doc_ids": doc_ids,
            "count": len(doc_ids)
        }, "文档添加成功")
        
    except Exception as e:
        print(f"添加文档错误: {e}")
        return error_response("添加文档失败，请稍后重试", 500)

@app.route('/api/documents/<doc_id>', methods=['PUT'])
def update_document(doc_id: str):
    try:
        data = request.get_json()
        
        is_valid, error_msg = validate_request_data(data, ['text'])
        if not is_valid:
            return error_response(error_msg)
        
        new_text = data['text']
        new_metadata = data.get('metadata', {})
        
        vector_manager.update_qa(doc_id, new_text, new_metadata)
        
        return success_response({
            "doc_id": doc_id
        }, "文档更新成功")
        
    except Exception as e:
        print(f"更新文档错误: {e}")
        return error_response("更新文档失败，请稍后重试", 500)

@app.route('/api/documents/<doc_id>', methods=['DELETE'])
def delete_document(doc_id: str):
    try:
        vector_manager.delete_qa(doc_id)
        
        return success_response({
            "doc_id": doc_id
        }, "文档删除成功")
        
    except Exception as e:
        print(f"删除文档错误: {e}")
        return error_response("删除文档失败，请稍后重试", 500)

@app.route('/api/documents/search', methods=['GET'])
def search_documents():
    try:
        query = request.args.get('query', '').strip()
        k = int(request.args.get('k', 4))
        with_score = request.args.get('with_score', 'false').lower() == 'true'
        
        if not query:
            return error_response("查询参数query不能为空")
        
        if k <= 0 or k > 20:
            return error_response("参数k必须在1-20之间")
        
        results = vector_manager.search_qa(query, k, with_score)
        
        if with_score:
            formatted_results = []
            for doc, score in results:
                formatted_results.append({
                    "content": doc.page_content,
                    "metadata": doc.metadata,
                    "similarity_score": float(score)
                })
        else:
            formatted_results = []
            for doc in results:
                formatted_results.append({
                    "content": doc.page_content,
                    "metadata": doc.metadata
                })
        
        return success_response({
            "results": formatted_results,
            "count": len(formatted_results),
            "query": query
        }, "搜索完成")
        
    except ValueError as e:
        return error_response(f"参数格式错误: {str(e)}")
    except Exception as e:
        print(f"搜索文档错误: {e}")
        return error_response("搜索失败，请稍后重试", 500)

@app.route('/api/chat/stream', methods=['POST'])
def chat_stream():
    try:
        data = request.get_json()
        
        is_valid, error_msg = validate_request_data(data, ['message'])
        if not is_valid:
            return error_response(error_msg)
        
        user_message = data['message'].strip()
        history = data.get('history', [])
        session_id = data.get('session_id', str(uuid.uuid4()))
        
        if not user_message:
            return error_response("消息内容不能为空")
        
        if history and not isinstance(history, list):
            return error_response("history必须是列表格式")
        
        formatted_history = []
        for item in history:
            if isinstance(item, dict) and 'role' in item and 'content' in item:
                if item['role'] in ['user', 'ai', 'assistant']:
                    role = 'ai' if item['role'] == 'assistant' else item['role']
                    formatted_history.append((role, item['content']))
            elif isinstance(item, (list, tuple)) and len(item) == 2:
                formatted_history.append((item[0], item[1]))
            else:
                return error_response("history格式错误，每项应包含role和content字段或为[role, content]格式")
        
        def generate():
            try:
                yield f"data: {json.dumps({'type': 'start', 'message': '', 'session_id': session_id}, ensure_ascii=False)}\n\n"
                
                full_response = ""
                for chunk in streaming_agent.stream_run(user_message, formatted_history):
                    full_response += chunk
                    yield f"data: {json.dumps({'type': 'chunk', 'message': chunk, 'session_id': session_id}, ensure_ascii=False)}\n\n"
                    time.sleep(0.01)
                
                yield f"data: {json.dumps({'type': 'end', 'message': full_response, 'session_id': session_id}, ensure_ascii=False)}\n\n"
                
            except Exception as e:
                print(f"流式生成错误: {e}")
                yield f"data: {json.dumps({'type': 'error', 'message': '抱歉，服务出现问题，请稍后重试', 'session_id': session_id}, ensure_ascii=False)}\n\n"
        
        return Response(
            generate(),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type',
                'Access-Control-Allow-Methods': 'POST, OPTIONS'
            }
        )
        
    except Exception as e:
        print(f"聊天接口错误: {e}")
        return error_response("聊天服务异常，请稍后重试", 500)

@app.route('/api/chat/summarize', methods=['POST'])
def summarize_dialogue():
    try:
        data = request.get_json()
        
        is_valid, error_msg = validate_request_data(data, ['history'])
        if not is_valid:
            return error_response(error_msg)
        
        history = data['history']
        session_id = data.get('session_id', str(uuid.uuid4()))
        
        if not isinstance(history, list) or len(history) == 0:
            return error_response("history必须是非空列表")
        
        formatted_history = []
        for item in history:
            if isinstance(item, dict) and 'role' in item and 'content' in item:
                if item['role'] in ['user', 'ai', 'assistant']:
                    role = 'ai' if item['role'] == 'assistant' else item['role']
                    formatted_history.append((role, item['content']))
            elif isinstance(item, (list, tuple)) and len(item) == 2:
                formatted_history.append((item[0], item[1]))
            else:
                return error_response("history格式错误，每项应包含role和content字段或为[role, content]格式")
        
        def generate():
            try:
                yield f"data: {json.dumps({'type': 'start', 'message': '', 'session_id': session_id}, ensure_ascii=False)}\n\n"
                
                full_response = ""
                for chunk in dialogue_summarizer.stream_summarize(formatted_history):
                    full_response += chunk
                    yield f"data: {json.dumps({'type': 'chunk', 'message': chunk, 'session_id': session_id}, ensure_ascii=False)}\n\n"
                    time.sleep(0.01)
                
                yield f"data: {json.dumps({'type': 'end', 'message': full_response, 'session_id': session_id}, ensure_ascii=False)}\n\n"
                
            except Exception as e:
                print(f"对话总结错误: {e}")
                yield f"data: {json.dumps({'type': 'error', 'message': '抱歉，总结服务出现问题，请稍后重试', 'session_id': session_id}, ensure_ascii=False)}\n\n"
        
        return Response(
            generate(),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type',
                'Access-Control-Allow-Methods': 'POST, OPTIONS'
            }
        )
        
    except Exception as e:
        print(f"总结接口错误: {e}")
        return error_response("对话总结服务异常，请稍后重试", 500)

@app.route('/api/health', methods=['GET'])
def health_check():
    return success_response({
        "status": "healthy",
        "service": "inspection-customer-service-v2"
    }, "服务运行正常")

def validate_ws_data(data: Dict[str, Any], required_fields: List[str]) -> tuple[bool, str]:
    if not data:
        return False, "请求数据不能为空"
    
    for field in required_fields:
        if field not in data:
            return False, f"缺少必需字段: {field}"
        if not data[field]:
            return False, f"字段 {field} 不能为空"
    
    return True, ""

@socketio.on('connect', namespace='/ws/chat')
def handle_connect():
    print(f"客户端连接: {request.sid}")
    emit('connected', {
        'type': 'connected',
        'message': '连接成功',
        'client_id': request.sid
    })

@socketio.on('disconnect', namespace='/ws/chat')
def handle_disconnect():
    print(f"客户端断开连接: {request.sid}")

@socketio.on('stream_chat', namespace='/ws/chat')
def handle_stream_chat(data):
    try:
        is_valid, error_msg = validate_ws_data(data, ['message'])
        if not is_valid:
            emit('error', {
                'type': 'error',
                'message': error_msg,
                'session_id': data.get('session_id', '')
            })
            return
        
        user_message = data['message'].strip()
        history = data.get('history', [])
        session_id = data.get('session_id', str(uuid.uuid4()))
        
        if not user_message:
            emit('error', {
                'type': 'error',
                'message': '消息内容不能为空',
                'session_id': session_id
            })
            return
        
        if history and not isinstance(history, list):
            emit('error', {
                'type': 'error',
                'message': 'history必须是列表格式',
                'session_id': session_id
            })
            return
        
        formatted_history = []
        for item in history:
            if isinstance(item, dict) and 'role' in item and 'content' in item:
                if item['role'] in ['user', 'ai', 'assistant']:
                    role = 'ai' if item['role'] == 'assistant' else item['role']
                    formatted_history.append((role, item['content']))
            elif isinstance(item, (list, tuple)) and len(item) == 2:
                formatted_history.append((item[0], item[1]))
            else:
                emit('error', {
                    'type': 'error',
                    'message': 'history格式错误，每项应包含role和content字段或为[role, content]格式',
                    'session_id': session_id
                })
                return
        
        emit('chat_response', {
            'type': 'start',
            'message': '',
            'session_id': session_id
        })
        
        full_response = ""
        try:
            for chunk in streaming_agent.stream_run(user_message, formatted_history):
                full_response += chunk
                emit('chat_response', {
                    'type': 'chunk',
                    'message': chunk,
                    'session_id': session_id
                })
                socketio.sleep(0.01)
            
            emit('chat_response', {
                'type': 'end',
                'message': full_response,
                'session_id': session_id
            })
            
        except Exception as e:
            print(f"WebSocket流式生成错误: {e}")
            emit('error', {
                'type': 'error',
                'message': '抱歉，服务出现问题，请稍后重试',
                'session_id': session_id
            })
        
    except Exception as e:
        print(f"WebSocket聊天接口错误: {e}")
        emit('error', {
            'type': 'error',
            'message': '聊天服务异常，请稍后重试',
            'session_id': data.get('session_id', '')
        })

@socketio.on('summarize_dialogue', namespace='/ws/chat')
def handle_summarize_dialogue(data):
    try:
        is_valid, error_msg = validate_ws_data(data, ['history'])
        if not is_valid:
            emit('error', {
                'type': 'error',
                'message': error_msg,
                'session_id': data.get('session_id', '')
            })
            return
        
        history = data['history']
        session_id = data.get('session_id', str(uuid.uuid4()))
        
        if not isinstance(history, list) or len(history) == 0:
            emit('error', {
                'type': 'error',
                'message': 'history必须是非空列表',
                'session_id': session_id
            })
            return
        
        formatted_history = []
        for item in history:
            if isinstance(item, dict) and 'role' in item and 'content' in item:
                if item['role'] in ['user', 'ai', 'assistant']:
                    role = 'ai' if item['role'] == 'assistant' else item['role']
                    formatted_history.append((role, item['content']))
            elif isinstance(item, (list, tuple)) and len(item) == 2:
                formatted_history.append((item[0], item[1]))
            else:
                emit('error', {
                    'type': 'error',
                    'message': 'history格式错误，每项应包含role和content字段或为[role, content]格式',
                    'session_id': session_id
                })
                return
        
        emit('summary_response', {
            'type': 'start',
            'message': '',
            'session_id': session_id
        })
        
        full_response = ""
        try:
            for chunk in dialogue_summarizer.stream_summarize(formatted_history):
                full_response += chunk
                emit('summary_response', {
                    'type': 'chunk',
                    'message': chunk,
                    'session_id': session_id
                })
                socketio.sleep(0.01)
            
            emit('summary_response', {
                'type': 'end',
                'message': full_response,
                'session_id': session_id
            })
            
        except Exception as e:
            print(f"WebSocket对话总结错误: {e}")
            emit('error', {
                'type': 'error',
                'message': '抱歉，总结服务出现问题，请稍后重试',
                'session_id': session_id
            })
        
    except Exception as e:
        print(f"WebSocket总结接口错误: {e}")
        emit('error', {
            'type': 'error',
            'message': '对话总结服务异常，请稍后重试',
            'session_id': data.get('session_id', '')
        })


if __name__ == '__main__':
    print("启动检测客服服务 v2.0...")
    print("服务地址: http://localhost:5000")
    print("WebSocket地址: ws://localhost:5000/ws/chat")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000) 