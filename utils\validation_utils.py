import re
from typing import Any, Dict, List, Optional, Union, Callable
from datetime import datetime

class ValidationError(Exception):
    def __init__(self, message: str, field: str = None):
        self.message = message
        self.field = field
        super().__init__(self.message)

class Validator:
    def __init__(self):
        self.rules = {}
        self.custom_validators = {}
        
    def add_rule(self, field: str, rule: str, message: str = None):
        if field not in self.rules:
            self.rules[field] = []
        self.rules[field].append({
            "rule": rule,
            "message": message or f"{field} validation failed"
        })
        
    def add_custom_validator(self, name: str, validator_func: Callable):
        self.custom_validators[name] = validator_func
        
    def validate_required(self, value: Any) -> bool:
        return value is not None and str(value).strip() != ""
        
    def validate_email(self, email: str) -> bool:
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, str(email)))
        
    def validate_phone(self, phone: str) -> bool:
        pattern = r'^1[3-9]\d{9}$'
        return bool(re.match(pattern, str(phone)))
        
    def validate_url(self, url: str) -> bool:
        pattern = r'^https?://[^\s/$.?#].[^\s]*$'
        return bool(re.match(pattern, str(url)))
        
    def validate_length(self, value: str, min_len: int = 0, max_len: int = None) -> bool:
        length = len(str(value))
        if length < min_len:
            return False
        if max_len and length > max_len:
            return False
        return True
        
    def validate_numeric(self, value: Any) -> bool:
        try:
            float(value)
            return True
        except (ValueError, TypeError):
            return False
            
    def validate_integer(self, value: Any) -> bool:
        try:
            int(value)
            return True
        except (ValueError, TypeError):
            return False
            
    def validate_range(self, value: Any, min_val: float = None, max_val: float = None) -> bool:
        try:
            num_value = float(value)
            if min_val is not None and num_value < min_val:
                return False
            if max_val is not None and num_value > max_val:
                return False
            return True
        except (ValueError, TypeError):
            return False
            
    def validate_pattern(self, value: str, pattern: str) -> bool:
        return bool(re.match(pattern, str(value)))
        
    def validate_date(self, date_str: str, format_str: str = "%Y-%m-%d") -> bool:
        try:
            datetime.strptime(str(date_str), format_str)
            return True
        except ValueError:
            return False
            
    def validate_in_choices(self, value: Any, choices: List[Any]) -> bool:
        return value in choices
        
    def validate_data(self, data: Dict[str, Any]) -> Dict[str, List[str]]:
        errors = {}
        
        for field, rules in self.rules.items():
            field_value = data.get(field)
            field_errors = []
            
            for rule_info in rules:
                rule = rule_info["rule"]
                message = rule_info["message"]
                
                if rule == "required":
                    if not self.validate_required(field_value):
                        field_errors.append(message)
                elif rule == "email":
                    if field_value and not self.validate_email(field_value):
                        field_errors.append(message)
                elif rule == "phone":
                    if field_value and not self.validate_phone(field_value):
                        field_errors.append(message)
                elif rule == "url":
                    if field_value and not self.validate_url(field_value):
                        field_errors.append(message)
                elif rule == "numeric":
                    if field_value and not self.validate_numeric(field_value):
                        field_errors.append(message)
                elif rule == "integer":
                    if field_value and not self.validate_integer(field_value):
                        field_errors.append(message)
                elif rule in self.custom_validators:
                    validator_func = self.custom_validators[rule]
                    if field_value and not validator_func(field_value):
                        field_errors.append(message)
                        
            if field_errors:
                errors[field] = field_errors
                
        return errors
        
    def is_valid(self, data: Dict[str, Any]) -> bool:
        errors = self.validate_data(data)
        return len(errors) == 0

class FormValidator:
    def __init__(self, form_data: Dict[str, Any]):
        self.form_data = form_data
        self.errors = {}
        
    def required(self, field: str, message: str = None) -> 'FormValidator':
        if field not in self.form_data or not str(self.form_data[field]).strip():
            self._add_error(field, message or f"{field} is required")
        return self
        
    def email(self, field: str, message: str = None) -> 'FormValidator':
        if field in self.form_data:
            value = self.form_data[field]
            if value and not Validator().validate_email(value):
                self._add_error(field, message or f"{field} must be a valid email")
        return self
        
    def min_length(self, field: str, min_len: int, message: str = None) -> 'FormValidator':
        if field in self.form_data:
            value = str(self.form_data[field])
            if len(value) < min_len:
                self._add_error(field, message or f"{field} must be at least {min_len} characters")
        return self
        
    def max_length(self, field: str, max_len: int, message: str = None) -> 'FormValidator':
        if field in self.form_data:
            value = str(self.form_data[field])
            if len(value) > max_len:
                self._add_error(field, message or f"{field} must not exceed {max_len} characters")
        return self
        
    def numeric(self, field: str, message: str = None) -> 'FormValidator':
        if field in self.form_data:
            value = self.form_data[field]
            if value and not Validator().validate_numeric(value):
                self._add_error(field, message or f"{field} must be a number")
        return self
        
    def _add_error(self, field: str, message: str):
        if field not in self.errors:
            self.errors[field] = []
        self.errors[field].append(message)
        
    def get_errors(self) -> Dict[str, List[str]]:
        return self.errors
        
    def is_valid(self) -> bool:
        return len(self.errors) == 0

def validate_request_data(data: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, List[str]]:
    validator = Validator()
    
    for field, rules in schema.items():
        if isinstance(rules, list):
            for rule in rules:
                validator.add_rule(field, rule)
        else:
            validator.add_rule(field, rules)
            
    return validator.validate_data(data)

def quick_validate(data: Dict[str, Any], required_fields: List[str] = None, 
                  email_fields: List[str] = None, phone_fields: List[str] = None) -> bool:
    validator = FormValidator(data)
    
    if required_fields:
        for field in required_fields:
            validator.required(field)
            
    if email_fields:
        for field in email_fields:
            validator.email(field)
            
    if phone_fields:
        for field in phone_fields:
            if field in data:
                phone_validator = Validator()
                if not phone_validator.validate_phone(data[field]):
                    return False
                    
    return validator.is_valid() 