class ChatInterface {
    constructor() {
        this.sessionId = 'session_' + Date.now();
        this.currentEventSource = null;
        this.isStreaming = false;
        this.currentAIMessage = null;
        
        this.initElements();
        this.bindEvents();
        this.initWelcomeMessage();
    }

    initElements() {
        this.chatMessages = document.getElementById('chatMessages');
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.chatForm = document.getElementById('chatForm');
        this.typingIndicator = document.getElementById('typingIndicator');
        this.charCount = document.querySelector('.char-count');
    }

    bindEvents() {
        this.chatForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.sendMessage();
        });

        this.messageInput.addEventListener('input', () => {
            this.updateCharCount();
            this.autoResize();
        });

        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        window.addEventListener('beforeunload', () => {
            if (this.currentEventSource) {
                this.currentEventSource.close();
            }
        });
    }

    initWelcomeMessage() {
        const welcomeTime = document.getElementById('welcomeTime');
        if (welcomeTime) {
            welcomeTime.textContent = this.formatTime(new Date());
        }
    }

    updateCharCount() {
        const length = this.messageInput.value.length;
        this.charCount.textContent = `${length}/500`;
        
        if (length > 450) {
            this.charCount.style.color = '#dc2626';
        } else if (length > 400) {
            this.charCount.style.color = '#f59e0b';
        } else {
            this.charCount.style.color = '#6b7280';
        }
    }

    autoResize() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
    }

    formatTime(date) {
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    async sendMessage() {
        const message = this.messageInput.value.trim();
        
        if (!message || this.isStreaming) {
            return;
        }

        this.addUserMessage(message);
        
        this.messageInput.value = '';
        this.updateCharCount();
        this.autoResize();
        
        this.setSendButtonState(false);
        
        this.showTypingIndicator();
        
        await this.sendToAI(message);
    }

    addUserMessage(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message user-message';
        messageDiv.innerHTML = `
            <div class="message-avatar">
                <div class="avatar user-avatar">👤</div>
            </div>
            <div class="message-content">
                <div class="message-text">${this.escapeHtml(message)}</div>
                <div class="message-time">${this.formatTime(new Date())}</div>
            </div>
        `;
        
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    createAIMessage() {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message ai-message';
        messageDiv.innerHTML = `
            <div class="message-avatar">
                <div class="avatar ai-avatar">🤖</div>
            </div>
            <div class="message-content">
                <div class="message-text typing-animation"></div>
                <div class="message-time">${this.formatTime(new Date())}</div>
            </div>
        `;
        
        this.chatMessages.appendChild(messageDiv);
        this.currentAIMessage = messageDiv.querySelector('.message-text');
        this.scrollToBottom();
        return this.currentAIMessage;
    }

    async sendToAI(message) {
        try {
            this.isStreaming = true;
            
            if (this.currentEventSource) {
                this.currentEventSource.close();
            }
            
            const response = await fetch('/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    session_id: this.sessionId
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const aiMessageElement = this.createAIMessage();
            
            this.hideTypingIndicator();
            
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            while (true) {
                const { done, value } = await reader.read();
                
                if (done) break;
                
                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';
                
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            this.handleStreamData(data, aiMessageElement);
                        } catch (e) {
                            console.warn('Failed to parse SSE data:', line);
                        }
                    }
                }
            }

        } catch (error) {
            console.error('Request failed:', error);
            this.handleError('网络请求失败，请稍后重试');
        } finally {
            this.isStreaming = false;
            this.setSendButtonState(true);
            this.hideTypingIndicator();
        }
    }

    handleStreamData(data, messageElement) {
        if (data.type === 'start') {
            messageElement.classList.remove('typing-animation');
            messageElement.textContent = '';
        } else if (data.type === 'chunk') {
            messageElement.textContent += data.message;
            this.scrollToBottom();
        } else if (data.type === 'end') {
            this.scrollToBottom();
        } else if (data.type === 'error') {
            this.handleError(data.message || '服务器错误');
        }
    }

    handleError(errorMessage) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message ai-message error';
        messageDiv.innerHTML = `
            <div class="message-avatar">
                <div class="avatar ai-avatar">🤖</div>
            </div>
            <div class="message-content">
                <div class="message-text">${errorMessage}</div>
                <div class="message-time">${this.formatTime(new Date())}</div>
            </div>
        `;
        
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    showTypingIndicator() {
        this.typingIndicator.style.display = 'flex';
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        this.typingIndicator.style.display = 'none';
    }

    setSendButtonState(enabled) {
        this.sendButton.disabled = !enabled;
        if (enabled) {
            this.sendButton.classList.remove('disabled');
        } else {
            this.sendButton.classList.add('disabled');
        }
    }

    scrollToBottom() {
        setTimeout(() => {
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }, 10);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

const chatInterface = new ChatInterface();