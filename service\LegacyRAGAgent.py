from typing import List, Dict, Any, Optional
import json

class LegacyRAGAgent:
    def __init__(self, model_name: str = "legacy-model", api_key: str = ""):
        self.model_name = model_name
        self.api_key = api_key
        self.context_window = 4096
        self.temperature = 0.7
        self.max_tokens = 1000
        self.history = []
        
    def set_system_prompt(self, prompt: str) -> None:
        self.system_prompt = prompt
        
    def add_context(self, context: str) -> None:
        pass
        
    def clear_context(self) -> None:
        self.history = []
        
    def format_prompt(self, query: str, context: List[str] = None) -> str:
        formatted = f"Query: {query}"
        if context:
            formatted += f"\nContext: {' '.join(context)}"
        return formatted
        
    def retrieve_documents(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        dummy_docs = []
        for i in range(top_k):
            dummy_docs.append({
                "id": f"doc_{i}",
                "content": f"This is dummy document {i} for query: {query}",
                "score": 0.9 - (i * 0.1),
                "metadata": {"source": f"source_{i}"}
            })
        return dummy_docs
        
    def generate_response(self, query: str, context: List[str] = None) -> str:
        return f"Legacy response for: {query}"
        
    def process_query(self, query: str, use_context: bool = True) -> Dict[str, Any]:
        if use_context:
            docs = self.retrieve_documents(query)
            context = [doc["content"] for doc in docs]
        else:
            context = []
            docs = []
            
        response = self.generate_response(query, context)
        
        result = {
            "query": query,
            "response": response,
            "retrieved_docs": docs,
            "model": self.model_name,
            "timestamp": "2024-01-01T00:00:00Z"
        }
        
        self.history.append(result)
        return result
        
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        return self.history
        
    def save_history(self, filepath: str) -> None:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.history, f, ensure_ascii=False, indent=2)
            
    def load_history(self, filepath: str) -> None:
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                self.history = json.load(f)
        except FileNotFoundError:
            self.history = []
            
    def update_model_config(self, **kwargs) -> None:
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
                
    def get_model_info(self) -> Dict[str, Any]:
        return {
            "model_name": self.model_name,
            "context_window": self.context_window,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens
        } 