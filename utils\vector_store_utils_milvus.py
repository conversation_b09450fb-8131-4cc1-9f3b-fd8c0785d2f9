from langchain_community.vectorstores import Milvus
from langchain.schema import Document
from langchain_huggingface import HuggingFaceEmbeddings
from pymilvus import connections
import os
import uuid
import torch
from config import Config

from pymilvus import utility, connections
class VectorStoreUtils:
    def __init__(self, collection_name: str = "default", embedding=None, host="***********", port="19530",persist_directory=None):
        self.collection_name = collection_name

        # 建立连接
        connections.connect(host=host, port=port)

         # 🚨开发调试用：如果 collection 存在并开启 auto_reset_collection，就先删除它
        # if utility.has_collection(collection_name):
        #     print(f"[VectorStoreUtils] Deleting existing collection: {collection_name}")
        #     utility.drop_collection(collection_name)

        # 初始化嵌入模型
        self.embedding = embedding or HuggingFaceEmbeddings(
            model_name=Config.get("EMBEDDING_MODEL_PATH") or "BAAI/bge-base-zh",
            model_kwargs={"device": "cuda" if torch.cuda.is_available() else "cpu"},
            encode_kwargs={"normalize_embeddings": True}
        )

        # 构建或加载 Milvus 向量库
        self.vectorstore = Milvus(
            collection_name=self.collection_name,
            embedding_function=self.embedding,
            connection_args={"host": host, "port": port},
        )
       

    def add_documents(self, texts: list[str], metadatas: list[dict] = None, ids: list[str] = None):
        metadatas = metadatas or [{}] * len(texts)
        ids = ids or [str(uuid.uuid4()) for _ in texts]
        docs = [Document(page_content=text, metadata=meta) for text, meta in zip(texts, metadatas)]
        self.vectorstore.add_texts(texts=texts, metadatas=metadatas, ids=ids)
        return ids

    def delete_document(self, doc_id: str):
        self.vectorstore.delete([doc_id])

    def update_document(self, doc_id: str, new_text: str, new_metadata: dict = None):
        self.delete_document(doc_id)
        self.add_documents(
            texts=[new_text],
            metadatas=[new_metadata or {}],
            ids=[doc_id]
        )

    def search(self, query: str, k: int = 4, with_score: bool = False):
        if with_score:
            return self.vectorstore.similarity_search_with_score(query, k=k)
        else:
            return self.vectorstore.similarity_search(query, k=k)

    def list_all_documents(self):
        raise NotImplementedError("Milvus does not support full document listing directly. Track documents externally.")
