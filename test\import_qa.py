"""
 导入标准问答结果
"""

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
导入数据脚本
用于将qa.json中的数据向量化并存入Chroma数据库
"""

import os
import json
import argparse
from tqdm import tqdm
from service.vector_store_manager import VectorStoreManager


def parse_jsonl(file_path):
    """解析JSON文件，返回文本和元数据列表

    Returns:
        texts: 需要向量化的文本（q 字段）
        metadatas: 包含原始qa元组的metadata
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    texts = []
    metadatas = []
    for item in data:
        q = item.get("q", "").strip()
        a = item.get("a", "").strip()
        if not q:
            continue
        texts.append(q)
        metadatas.append({"question": q, "answer": a})  # 你可以按需扩展字段
    return texts, metadatas

def import_data(jsonl_path,batch_size=100):
    """导入数据到向量数据库
    
    Args:
        jsonl_path: JSONL文件路径
        batch_size: 批处理大小
    
    Returns:
        导入的数据条数
    """
    print(f"开始导入数据: {jsonl_path}")
    
    # 初始化向量存储
    vector_store = VectorStoreManager()
    
    # 解析JSONL文件
    print("正在解析JSONL文件...")
    texts, metadatas = parse_jsonl(jsonl_path)
    print(f"共提取 {len(texts)} 条有效问答")

    if not texts:
        print("未找到有效问句，导入终止")
        return 0

    total_imported = 0
    for i in tqdm(range(0, len(texts), batch_size), desc="导入进度"):
        batch_texts = texts[i:i + batch_size]
        batch_metas = metadatas[i:i + batch_size]
        vector_store.add_qa(batch_texts, batch_metas)
        total_imported += len(batch_texts)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="导入数据到向量数据库")
    parser.add_argument("--jsonl-path", type=str, default="./test/qa.json",
                        help="JSONL文件路径")
    
    args = parser.parse_args()
    
    # 确保文件存在
    if not os.path.exists(args.jsonl_path):
        print(f"错误: 文件不存在: {args.jsonl_path}")
        exit(1)
    
    # 导入数据
    import_data(args.jsonl_path) 