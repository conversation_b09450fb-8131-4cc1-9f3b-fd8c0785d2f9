from typing import List, Tuple, Generator, Dict, Any
from langchain.schema import HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI
from config import Config

class DialogueSummarizer:
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model_name=Config.get("LLM_DIALOGUE_SUMMARIZER_MODEL_NAME"),
            base_url=Config.get("LLM_DIALOGUE_SUMMARIZER_BASE_URL"),
            api_key=Config.get("LLM_DIALOGUE_SUMMARIZER_API_KEY"),
            streaming=True
        )
    
    def _get_summarize_prompt(self) -> str:
        return """

        请阅读以下用户与AI的对话内容，并将其总结为一段简洁清晰的文字，供人工客服查阅。总结应涵盖以下信息，且仅输出一段自然流畅、完整准确的文字：

        - 用户的主要问题或请求（如咨询内容、需求意图）；
        - AI已提供的建议、回答或引导；
        - 用户的反馈或当前状态（是否达成初步目的、是否需要人工协助等）；
        - **如对话中包含用户的姓名、地址、电话、邮箱、账号等关键信息，必须完整提取并明确写入总结中，绝不可遗漏**；
    
        > 仅输出一段文字，无需列点或标题，语言自然、表达准确、信息完整。

        """
    
    def _format_dialogue_history(self, history: List[Tuple[str, str]]) -> str:
        formatted_lines = []
        
        for i, (role, content) in enumerate(history, 1):
            if role == "user":
                formatted_lines.append(f"用户[{i}]: {content}")
            elif role in ["ai", "assistant"]:
                formatted_lines.append(f"AI客服[{i}]: {content}")
        
        return "\n".join(formatted_lines)
    
    def stream_summarize(self, history: List[Tuple[str, str]]) -> Generator[str, None, None]:
        
        if not history:
            yield "对话历史为空，无法生成总结。"
            return
        
        dialogue_text = self._format_dialogue_history(history)
        
        messages = [
            SystemMessage(content=self._get_summarize_prompt()),
            HumanMessage(content=f"请总结以下对话内容：\n\n{dialogue_text}")
        ]
        
        for chunk in self.llm.stream(messages):
            if chunk.content:
                yield chunk.content
    
    def summarize(self, history: List[Tuple[str, str]]) -> str:
        
        if not history:
            return "对话历史为空，无法生成总结。"
        
        dialogue_text = self._format_dialogue_history(history)
        
        messages = [
            SystemMessage(content=self._get_summarize_prompt()),
            HumanMessage(content=f"请总结以下对话内容：\n\n{dialogue_text}")
        ]
        
        response = self.llm.invoke(messages)
        return response.content.strip()
    
    def analyze_dialogue_topics(self, history: List[Tuple[str, str]]) -> Dict[str, Any]:
        
        if not history:
            return {"topics": [], "sentiment": "neutral", "summary": ""}
        
        dialogue_text = self._format_dialogue_history(history)
        
        analysis_prompt = """
        ## 任务
        分析以下对话的主要主题和用户情感倾向。
        
        ## 输出格式
        请用JSON格式输出：
        {
            "topics": ["主题1", "主题2"],
            "sentiment": "positive/neutral/negative", 
            "key_keywords": ["关键词1", "关键词2"],
            "user_satisfaction": "satisfied/neutral/unsatisfied"
        }
        """
        
        messages = [
            SystemMessage(content=analysis_prompt),
            HumanMessage(content=dialogue_text)
        ]
        
        try:
            response = self.llm.invoke(messages)
            return {"analysis": response.content.strip()}
        except Exception as e:
            return {"error": f"分析失败: {str(e)}"}

