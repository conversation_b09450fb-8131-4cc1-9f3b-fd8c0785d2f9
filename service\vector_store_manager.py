from utils.vector_store_utils import VectorStoreUtils
from config import Config

class VectorStoreManager:
    def __init__(self):
        self.qa_store = VectorStoreUtils(
            persist_directory=Config.get("QA_VECTOR_DB_PATH"),
            collection_name="qa_collection"
        )

    def add_qa(self, texts: list[str], metadatas: list[dict] = None):
        return self.qa_store.add_documents(texts, metadatas)

    def search_qa(self, query: str, k: int = 4, with_score: bool = False):
        return self.qa_store.search(query, k, with_score)

    def update_qa(self, doc_id: str, new_text: str, new_metadata: dict = None):
        return self.qa_store.update_document(doc_id, new_text, new_metadata)

    def delete_qa(self, doc_id: str):
        return self.qa_store.delete_document(doc_id)

