/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

/* 聊天容器 */
.chat-container {
    width: 100%;
    max-width: 800px;
    height: 90vh;
    max-height: 700px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 聊天头部 */
.chat-header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    padding: 20px;
    border-radius: 16px 16px 0 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    font-size: 32px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 8px;
    backdrop-filter: blur(10px);
}

.logo-text h1 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 2px;
}

.logo-text p {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 300;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10b981;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 消息区域 */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8fafc;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.message {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    opacity: 0;
    animation: fadeInUp 0.3s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.user-message {
    flex-direction: row-reverse;
}

.message-avatar {
    flex-shrink: 0;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 500;
}

.ai-avatar {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
}

.user-avatar {
    background: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
    color: white;
}

.message-content {
    flex: 1;
    max-width: 70%;
}

.user-message .message-content {
    text-align: right;
}

.message-text {
    background: white;
    padding: 16px 20px;
    border-radius: 20px;
    line-height: 1.5;
    color: #1f2937;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    position: relative;
    word-wrap: break-word;
}

.user-message .message-text {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    border-radius: 20px 20px 4px 20px;
}

.ai-message .message-text {
    border-radius: 20px 20px 20px 4px;
}

.message-time {
    font-size: 12px;
    color: #6b7280;
    margin-top: 6px;
    padding: 0 4px;
}

.user-message .message-time {
    text-align: right;
}

/* 输入区域 */
.chat-input-container {
    background: white;
    border-top: 1px solid #e5e7eb;
    padding: 20px;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    color: #6b7280;
    font-size: 14px;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: #6b7280;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.input-wrapper {
    display: flex;
    gap: 12px;
    align-items: flex-end;
    background: #f1f5f9;
    border-radius: 16px;
    padding: 12px;
    border: 2px solid transparent;
    transition: all 0.2s ease;
}

.input-wrapper:focus-within {
    border-color: #4f46e5;
    background: white;
    box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1);
}

#messageInput {
    flex: 1;
    border: none;
    background: transparent;
    outline: none;
    resize: none;
    font-family: inherit;
    font-size: 16px;
    line-height: 1.5;
    color: #1f2937;
    max-height: 120px;
    min-height: 24px;
}

#messageInput::placeholder {
    color: #9ca3af;
}

.send-button {
    width: 44px;
    height: 44px;
    border: none;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    border-radius: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}



.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    font-size: 12px;
    color: #6b7280;
}

.char-count {
    font-weight: 500;
}

.tip {
    font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .chat-container {
        height: 100vh;
        max-height: none;
        border-radius: 0;
    }
    
    .chat-header {
        border-radius: 0;
        padding: 16px;
    }
    
    .logo-icon {
        font-size: 28px;
        padding: 6px;
    }
    
    .logo-text h1 {
        font-size: 20px;
    }
    
    .chat-messages {
        padding: 16px;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .chat-input-container {
        padding: 16px;
    }
    
    .input-footer .tip {
        display: none;
    }
}

/* 流式打字效果 */
.typing-animation {
    position: relative;
}

.typing-animation::after {
    content: '|';
    animation: blink 1s infinite;
    color: #4f46e5;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* 错误消息样式 */
.error-message .message-text {
    background: #fee2e2;
    color: #dc2626;
    border-left: 4px solid #dc2626;
} 