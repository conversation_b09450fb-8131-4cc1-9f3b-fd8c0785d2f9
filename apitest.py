

import json
import pandas as pd
from openai import OpenAI
import concurrent.futures
from tqdm import tqdm
import re
import os   

BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
API_KEY = "sk-1de5f600058f490994837e558be5f010"

MODEL = "qwen-turbo-0624"


client = OpenAI(api_key=API_KEY, base_url=BASE_URL)



def chat(text):

    prompt = """
        你是哪一家公司开发的，模型版本是什么？

    """
  

    
    user_prompt = prompt.replace("{text}", text)
    
    response = client.chat.completions.create(
        model=MODEL,
        messages=[
            {"role": "user", "content": user_prompt}
        ],
        temperature=0.1
    )

    if response.choices[0].message.content:
        return response.choices[0].message.content
    else:
        print(f"Error in model response: {response}")
        return None



if __name__ == "__main__":
    print(chat("你是谁？"))