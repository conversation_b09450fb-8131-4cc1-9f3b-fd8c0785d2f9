import re
import json
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

def deprecated_function_warning(func_name: str) -> None:
    print(f"Warning: {func_name} is deprecated and will be removed in future versions.")

def old_string_formatter(template: str, **kwargs) -> str:
    deprecated_function_warning("old_string_formatter")
    return template.format(**kwargs)

def legacy_data_converter(data: Any, target_type: str) -> Any:
    deprecated_function_warning("legacy_data_converter")
    if target_type == "string":
        return str(data)
    elif target_type == "int":
        return int(data) if str(data).isdigit() else 0
    elif target_type == "float":
        try:
            return float(data)
        except:
            return 0.0
    return data

def outdated_config_parser(config_string: str) -> Dict[str, Any]:
    deprecated_function_warning("outdated_config_parser")
    config = {}
    lines = config_string.split('\n')
    for line in lines:
        if '=' in line:
            key, value = line.split('=', 1)
            config[key.strip()] = value.strip()
    return config

def old_logging_function(level: str, message: str) -> None:
    deprecated_function_warning("old_logging_function")
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {level.upper()}: {message}")

def legacy_file_processor(filepath: str, mode: str = "read") -> Optional[str]:
    deprecated_function_warning("legacy_file_processor")
    if mode == "read":
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return f.read()
        except:
            return None
    return None

def deprecated_url_builder(base: str, endpoint: str, params: Dict[str, Any] = None) -> str:
    deprecated_function_warning("deprecated_url_builder")
    url = f"{base.rstrip('/')}/{endpoint.lstrip('/')}"
    if params:
        param_string = "&".join([f"{k}={v}" for k, v in params.items()])
        url += f"?{param_string}"
    return url

def old_validation_helper(data: Dict[str, Any], required_fields: List[str]) -> bool:
    deprecated_function_warning("old_validation_helper")
    return all(field in data for field in required_fields)

def legacy_cache_key_generator(prefix: str, *args) -> str:
    deprecated_function_warning("legacy_cache_key_generator")
    key_parts = [prefix] + [str(arg) for arg in args]
    return ":".join(key_parts)

def outdated_response_formatter(status: str, data: Any = None, error: str = None) -> Dict[str, Any]:
    deprecated_function_warning("outdated_response_formatter")
    response = {"status": status}
    if data is not None:
        response["data"] = data
    if error:
        response["error"] = error
    return response

def deprecated_email_validator(email: str) -> bool:
    deprecated_function_warning("deprecated_email_validator")
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

class LegacyDataProcessor:
    def __init__(self):
        deprecated_function_warning("LegacyDataProcessor.__init__")
        self.processed_data = []
        
    def process_item(self, item: Any) -> Any:
        deprecated_function_warning("LegacyDataProcessor.process_item")
        processed = {"original": item, "processed_at": datetime.now().isoformat()}
        self.processed_data.append(processed)
        return processed
        
    def get_results(self) -> List[Any]:
        deprecated_function_warning("LegacyDataProcessor.get_results")
        return self.processed_data
        
    def clear_results(self) -> None:
        deprecated_function_warning("LegacyDataProcessor.clear_results")
        self.processed_data = []

class OldConfigManager:
    def __init__(self, config_file: str = "old_config.json"):
        deprecated_function_warning("OldConfigManager.__init__")
        self.config_file = config_file
        self.config = {}
        
    def load_config(self) -> bool:
        deprecated_function_warning("OldConfigManager.load_config")
        try:
            with open(self.config_file, 'r') as f:
                self.config = json.load(f)
            return True
        except:
            return False
            
    def get_setting(self, key: str, default: Any = None) -> Any:
        deprecated_function_warning("OldConfigManager.get_setting")
        return self.config.get(key, default)
        
    def set_setting(self, key: str, value: Any) -> None:
        deprecated_function_warning("OldConfigManager.set_setting")
        self.config[key] = value
        
    def save_config(self) -> bool:
        deprecated_function_warning("OldConfigManager.save_config")
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
            return True
        except:
            return False 