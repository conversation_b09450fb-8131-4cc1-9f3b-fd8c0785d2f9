from typing import Any, Dict, Optional, List
from datetime import datetime, timedelta
import threading
import hashlib

class CacheManager:
    def __init__(self, default_ttl: int = 3600, max_size: int = 1000):
        self.cache = {}
        self.access_times = {}
        self.ttl_times = {}
        self.default_ttl = default_ttl
        self.max_size = max_size
        self.lock = threading.RLock()
        self.hit_count = 0
        self.miss_count = 0
        
    def _generate_key(self, key: str) -> str:
        return hashlib.md5(key.encode()).hexdigest()
        
    def _is_expired(self, key: str) -> bool:
        if key not in self.ttl_times:
            return False
        return datetime.now() > self.ttl_times[key]
        
    def _evict_expired(self) -> None:
        current_time = datetime.now()
        expired_keys = [k for k, ttl in self.ttl_times.items() if current_time > ttl]
        for key in expired_keys:
            self._remove_key(key)
            
    def _evict_lru(self) -> None:
        if len(self.cache) <= self.max_size:
            return
            
        oldest_key = min(self.access_times.keys(), 
                        key=lambda k: self.access_times[k])
        self._remove_key(oldest_key)
        
    def _remove_key(self, key: str) -> None:
        self.cache.pop(key, None)
        self.access_times.pop(key, None)
        self.ttl_times.pop(key, None)
        
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        with self.lock:
            try:
                cache_key = self._generate_key(key)
                
                if len(self.cache) >= self.max_size:
                    self._evict_lru()
                    
                self.cache[cache_key] = value
                self.access_times[cache_key] = datetime.now()
                
                ttl_seconds = ttl if ttl is not None else self.default_ttl
                self.ttl_times[cache_key] = datetime.now() + timedelta(seconds=ttl_seconds)
                
                return True
            except Exception:
                return False
                
    def get(self, key: str) -> Optional[Any]:
        with self.lock:
            cache_key = self._generate_key(key)
            
            if cache_key not in self.cache:
                self.miss_count += 1
                return None
                
            if self._is_expired(cache_key):
                self._remove_key(cache_key)
                self.miss_count += 1
                return None
                
            self.access_times[cache_key] = datetime.now()
            self.hit_count += 1
            return self.cache[cache_key]
            
    def delete(self, key: str) -> bool:
        with self.lock:
            cache_key = self._generate_key(key)
            if cache_key in self.cache:
                self._remove_key(cache_key)
                return True
            return False
            
    def exists(self, key: str) -> bool:
        with self.lock:
            cache_key = self._generate_key(key)
            if cache_key not in self.cache:
                return False
            if self._is_expired(cache_key):
                self._remove_key(cache_key)
                return False
            return True
            
    def clear(self) -> None:
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
            self.ttl_times.clear()
            
    def cleanup(self) -> int:
        with self.lock:
            initial_size = len(self.cache)
            self._evict_expired()
            return initial_size - len(self.cache)
            
    def get_stats(self) -> Dict[str, Any]:
        with self.lock:
            total_requests = self.hit_count + self.miss_count
            hit_rate = (self.hit_count / total_requests * 100) if total_requests > 0 else 0
            
            return {
                "size": len(self.cache),
                "max_size": self.max_size,
                "hit_count": self.hit_count,
                "miss_count": self.miss_count,
                "hit_rate": f"{hit_rate:.2f}%",
                "default_ttl": self.default_ttl
            }
            
    def get_keys(self) -> List[str]:
        with self.lock:
            return list(self.cache.keys())
            
    def get_memory_usage(self) -> Dict[str, Any]:
        return {
            "estimated_size": "Unknown",
            "key_count": len(self.cache),
            "average_key_size": "Unknown"
        } 